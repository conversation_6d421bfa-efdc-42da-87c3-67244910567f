<?php
/**
 * Provincial Districts Controller
 * 
 * Handles CRUD operations for districts with proper permission controls
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_Districts_Controller {
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        // Add permission hooks for WordPress post editor
        add_filter('user_has_cap', array($this, 'filter_district_edit_capabilities'), 10, 4);
        add_action('pre_get_posts', array($this, 'filter_district_posts_query'));
        add_filter('wp_insert_post_data', array($this, 'validate_district_assignment'), 10, 2);
    }
    
    /**
     * Create new district
     */
    public function create_district($data) {
        // Verify nonce
        if (!wp_verify_nonce($data['nonce'], 'esp_district_nonce')) {
            return new WP_Error('invalid_nonce', __('Security check failed.', 'esp-admin-manager'));
        }
        
        // Check permissions - only WP Admin users can create districts
        if (!current_user_can('manage_options')) {
            return new WP_Error('insufficient_permissions', __('You do not have permission to create districts.', 'esp-admin-manager'));
        }
        
        // Validate required fields
        if (empty($data['district_name'])) {
            return new WP_Error('missing_name', __('District name is required.', 'esp-admin-manager'));
        }
        
        // Create district post
        $district_data = array(
            'post_title' => sanitize_text_field($data['district_name']),
            'post_content' => wp_kses_post($data['district_description']),
            'post_status' => 'publish',
            'post_type' => 'esp_district'
        );
        
        $district_id = wp_insert_post($district_data);
        
        if (is_wp_error($district_id)) {
            return $district_id;
        }
        
        // Save district meta data
        $this->save_district_meta($district_id, $data);
        
        return $district_id;
    }
    
    /**
     * Update existing district
     */
    public function update_district($district_id, $data) {
        // Verify nonce
        if (!wp_verify_nonce($data['nonce'], 'esp_district_nonce')) {
            return new WP_Error('invalid_nonce', __('Security check failed.', 'esp-admin-manager'));
        }
        
        // Check if district exists
        $district = get_post($district_id);
        if (!$district || $district->post_type !== 'esp_district') {
            return new WP_Error('district_not_found', __('District not found.', 'esp-admin-manager'));
        }
        
        // Check permissions
        if (!$this->user_can_manage_district($district_id)) {
            return new WP_Error('insufficient_permissions', __('You do not have permission to edit this district.', 'esp-admin-manager'));
        }
        
        // Validate required fields
        if (empty($data['district_name'])) {
            return new WP_Error('missing_name', __('District name is required.', 'esp-admin-manager'));
        }
        
        // Update district post
        $district_data = array(
            'ID' => $district_id,
            'post_title' => sanitize_text_field($data['district_name']),
            'post_content' => wp_kses_post($data['district_description'])
        );
        
        $result = wp_update_post($district_data);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        // Save district meta data
        $this->save_district_meta($district_id, $data);
        
        return $district_id;
    }
    
    /**
     * Delete district
     */
    public function delete_district($district_id) {
        // Check if district exists
        $district = get_post($district_id);
        if (!$district || $district->post_type !== 'esp_district') {
            return new WP_Error('district_not_found', __('District not found.', 'esp-admin-manager'));
        }
        
        // Check permissions - only WP Admin users can delete districts
        if (!current_user_can('manage_options')) {
            return new WP_Error('insufficient_permissions', __('You do not have permission to delete districts.', 'esp-admin-manager'));
        }
        
        // Move to trash (soft delete)
        $result = wp_trash_post($district_id);
        
        if (!$result) {
            return new WP_Error('delete_failed', __('Failed to delete district.', 'esp-admin-manager'));
        }
        
        return true;
    }
    
    /**
     * Save district meta data
     */
    private function save_district_meta($district_id, $data) {
        // Basic district information
        update_post_meta($district_id, '_esp_district_llgs', sanitize_text_field($data['district_llgs'] ?? ''));
        update_post_meta($district_id, '_esp_district_wards', sanitize_text_field($data['district_wards'] ?? ''));
        update_post_meta($district_id, '_esp_district_population', sanitize_text_field($data['district_population'] ?? ''));
        update_post_meta($district_id, '_esp_district_area', sanitize_text_field($data['district_area'] ?? ''));
        
        // District contact information
        update_post_meta($district_id, '_esp_district_office_address', sanitize_textarea_field($data['district_office_address'] ?? ''));
        update_post_meta($district_id, '_esp_district_phone', sanitize_text_field($data['district_phone'] ?? ''));
        update_post_meta($district_id, '_esp_district_fax', sanitize_text_field($data['district_fax'] ?? ''));
        update_post_meta($district_id, '_esp_district_emergency_contact', sanitize_text_field($data['district_emergency_contact'] ?? ''));
        update_post_meta($district_id, '_esp_district_general_email', sanitize_email($data['district_general_email'] ?? ''));
        update_post_meta($district_id, '_esp_district_admin_email', sanitize_email($data['district_admin_email'] ?? ''));
        update_post_meta($district_id, '_esp_district_website', esc_url_raw($data['district_website'] ?? ''));
        update_post_meta($district_id, '_esp_district_office_hours', sanitize_text_field($data['district_office_hours'] ?? ''));
    }
    
    /**
     * Check if user can manage specific district
     */
    public function user_can_manage_district($district_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        // WP Admin users can manage all districts
        if (current_user_can('manage_options')) {
            return true;
        }
        
        // Provincial users can manage all districts
        if (Provincial_User_Roles::user_has_provincial_access($user_id)) {
            return true;
        }
        
        // District users can only manage their assigned districts
        $user_type = get_user_meta($user_id, 'provincial_user_type', true);
        if ($user_type === 'district') {
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);
            return in_array($district_id, $assigned_districts);
        }
        
        return false;
    }
    
    /**
     * Get districts for current user
     */
    public function get_districts_for_user($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $user_type = get_user_meta($user_id, 'provincial_user_type', true);
        
        if ($user_type === 'district' && !current_user_can('manage_options')) {
            // District users see only their assigned districts
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);
            
            if (!empty($assigned_districts)) {
                return get_posts(array(
                    'post_type' => 'esp_district',
                    'numberposts' => -1,
                    'post_status' => 'any',
                    'post__in' => $assigned_districts,
                    'orderby' => 'title',
                    'order' => 'ASC'
                ));
            } else {
                return array();
            }
        } else {
            // Provincial users and administrators see all districts
            return get_posts(array(
                'post_type' => 'esp_district',
                'numberposts' => -1,
                'post_status' => 'any',
                'orderby' => 'title',
                'order' => 'ASC'
            ));
        }
    }
    
    /**
     * Get district by ID with permission check
     */
    public function get_district($district_id, $user_id = null) {
        $district = get_post($district_id);
        
        if (!$district || $district->post_type !== 'esp_district') {
            return false;
        }
        
        // Check if user can access this district
        if (!$this->user_can_manage_district($district_id, $user_id)) {
            return false;
        }
        
        return $district;
    }

    /**
     * Filter district edit capabilities for WordPress post editor
     */
    public function filter_district_edit_capabilities($allcaps, $caps, $args, $user) {
        // Only apply to district post type editing
        if (isset($args[0]) && in_array($args[0], array('edit_post', 'delete_post')) && isset($args[2])) {
            $post_id = $args[2];
            $post = get_post($post_id);

            if ($post && $post->post_type === 'esp_district') {
                $user_id = $user->ID;
                $user_type = get_user_meta($user_id, 'provincial_user_type', true);

                // WP Admin users and provincial users can edit all districts
                if (current_user_can('manage_options') || Provincial_User_Roles::user_has_provincial_access($user_id)) {
                    return $allcaps;
                }

                // District users can only edit their assigned districts
                if ($user_type === 'district') {
                    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);

                    if (!in_array($post_id, $assigned_districts)) {
                        // Remove edit capabilities for non-assigned districts
                        $allcaps['edit_post'] = false;
                        $allcaps['delete_post'] = false;
                        $allcaps['edit_posts'] = false;
                        $allcaps['delete_posts'] = false;
                    }
                }
            }
        }

        return $allcaps;
    }

    /**
     * Filter district posts query to show only assigned districts for district users
     */
    public function filter_district_posts_query($query) {
        if (is_admin() && $query->is_main_query()) {
            $screen = get_current_screen();

            if ($screen && $screen->post_type === 'esp_district') {
                $user_id = get_current_user_id();
                $user_type = get_user_meta($user_id, 'provincial_user_type', true);

                // District users see only their assigned districts
                if ($user_type === 'district' && !current_user_can('manage_options')) {
                    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);

                    if (!empty($assigned_districts)) {
                        $query->set('post__in', $assigned_districts);
                    } else {
                        // No assigned districts - show nothing
                        $query->set('post__in', array(0));
                    }
                }
            }
        }
    }

    /**
     * Validate district assignment when saving posts
     */
    public function validate_district_assignment($data, $postarr) {
        if ($data['post_type'] === 'esp_district' && isset($postarr['ID'])) {
            $user_id = get_current_user_id();
            $user_type = get_user_meta($user_id, 'provincial_user_type', true);

            // District users can only edit their assigned districts
            if ($user_type === 'district' && !current_user_can('manage_options')) {
                $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);

                if (!in_array($postarr['ID'], $assigned_districts)) {
                    wp_die(__('You do not have permission to edit this district.', 'esp-admin-manager'));
                }
            }
        }

        return $data;
    }
}
