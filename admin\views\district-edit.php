<?php
/**
 * Provincial Administration Manager - District Edit View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get district ID from URL
$district_id = isset($_GET['district_id']) ? intval($_GET['district_id']) : 0;

if (!$district_id) {
    wp_die(__('Invalid district ID.', 'esp-admin-manager'));
}

// Get district controller
$districts_controller = Provincial_Districts_Controller::get_instance();

// Get district with permission check
$district = $districts_controller->get_district($district_id);

if (!$district) {
    wp_die(__('District not found or you do not have permission to edit this district.', 'esp-admin-manager'));
}

// Get district meta data
$district_llgs = get_post_meta($district_id, '_esp_district_llgs', true);
$district_wards = get_post_meta($district_id, '_esp_district_wards', true);
$district_population = get_post_meta($district_id, '_esp_district_population', true);
$district_area = get_post_meta($district_id, '_esp_district_area', true);
$district_office_address = get_post_meta($district_id, '_esp_district_office_address', true);
$district_phone = get_post_meta($district_id, '_esp_district_phone', true);
$district_fax = get_post_meta($district_id, '_esp_district_fax', true);
$district_emergency_contact = get_post_meta($district_id, '_esp_district_emergency_contact', true);
$district_general_email = get_post_meta($district_id, '_esp_district_general_email', true);
$district_admin_email = get_post_meta($district_id, '_esp_district_admin_email', true);
$district_website = get_post_meta($district_id, '_esp_district_website', true);
$district_office_hours = get_post_meta($district_id, '_esp_district_office_hours', true);

// Check if user is district user
$current_user_id = get_current_user_id();
$user_type = get_user_meta($current_user_id, 'provincial_user_type', true);
$is_district_user = ($user_type === 'district') && !current_user_can('manage_options');
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Edit District', 'esp-admin-manager'); ?>: <?php echo esc_html($district->post_title); ?></h1>
    <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts'); ?>" class="page-title-action"><?php _e('Back to Districts', 'esp-admin-manager'); ?></a>
    <hr class="wp-header-end">

    <?php settings_errors('esp_messages'); ?>

    <?php if ($is_district_user): ?>
        <div class="notice notice-info">
            <p><strong><?php _e('Note:', 'esp-admin-manager'); ?></strong> <?php _e('You can edit the information for this district as it is assigned to you.', 'esp-admin-manager'); ?></p>
        </div>
    <?php endif; ?>

    <form method="post" action="<?php echo admin_url('admin.php?page=provincial-admin-districts'); ?>" class="esp-form">
        <?php wp_nonce_field('esp_district_nonce', 'nonce'); ?>
        <input type="hidden" name="action" value="update_district">
        <input type="hidden" name="district_id" value="<?php echo esc_attr($district_id); ?>">

        <div class="esp-form-section">
            <h3><?php _e('Basic Information', 'esp-admin-manager'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="district_name"><?php _e('District Name', 'esp-admin-manager'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" id="district_name" name="district_name" class="regular-text" required 
                               value="<?php echo esc_attr($_POST['district_name'] ?? $district->post_title); ?>">
                        <p class="description"><?php _e('Enter the official name of the district', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_description"><?php _e('Description', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <?php 
                        wp_editor(
                            $_POST['district_description'] ?? $district->post_content,
                            'district_description',
                            array(
                                'textarea_name' => 'district_description',
                                'textarea_rows' => 8,
                                'media_buttons' => true,
                                'teeny' => false
                            )
                        );
                        ?>
                        <p class="description"><?php _e('Provide a detailed description of the district', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('District Statistics', 'esp-admin-manager'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="district_llgs"><?php _e('Local Level Governments (LLGs)', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="district_llgs" name="district_llgs" class="small-text" min="0"
                               value="<?php echo esc_attr($_POST['district_llgs'] ?? $district_llgs); ?>">
                        <p class="description"><?php _e('Number of Local Level Governments in this district', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_wards"><?php _e('Wards', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="district_wards" name="district_wards" class="small-text" min="0"
                               value="<?php echo esc_attr($_POST['district_wards'] ?? $district_wards); ?>">
                        <p class="description"><?php _e('Number of wards in this district', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_population"><?php _e('Population', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="district_population" name="district_population" class="regular-text" min="0"
                               value="<?php echo esc_attr($_POST['district_population'] ?? $district_population); ?>">
                        <p class="description"><?php _e('Total population of the district', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_area"><?php _e('Area (km²)', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="district_area" name="district_area" class="regular-text" min="0" step="0.01"
                               value="<?php echo esc_attr($_POST['district_area'] ?? $district_area); ?>">
                        <p class="description"><?php _e('Total area of the district in square kilometers', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Contact Information', 'esp-admin-manager'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="district_office_address"><?php _e('District Office Address', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="district_office_address" name="district_office_address" rows="3" class="large-text"><?php echo esc_textarea($_POST['district_office_address'] ?? $district_office_address); ?></textarea>
                        <p class="description"><?php _e('Physical address of the district office', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="tel" id="district_phone" name="district_phone" class="regular-text"
                               value="<?php echo esc_attr($_POST['district_phone'] ?? $district_phone); ?>">
                        <p class="description"><?php _e('Main district office phone number', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_fax"><?php _e('Fax Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="tel" id="district_fax" name="district_fax" class="regular-text"
                               value="<?php echo esc_attr($_POST['district_fax'] ?? $district_fax); ?>">
                        <p class="description"><?php _e('District office fax number', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_emergency_contact"><?php _e('Emergency Contact', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="tel" id="district_emergency_contact" name="district_emergency_contact" class="regular-text"
                               value="<?php echo esc_attr($_POST['district_emergency_contact'] ?? $district_emergency_contact); ?>">
                        <p class="description"><?php _e('Emergency contact number for the district', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_general_email"><?php _e('General Email', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="district_general_email" name="district_general_email" class="regular-text"
                               value="<?php echo esc_attr($_POST['district_general_email'] ?? $district_general_email); ?>">
                        <p class="description"><?php _e('General inquiries email address', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_admin_email"><?php _e('Administrative Email', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="district_admin_email" name="district_admin_email" class="regular-text"
                               value="<?php echo esc_attr($_POST['district_admin_email'] ?? $district_admin_email); ?>">
                        <p class="description"><?php _e('Official administrative email address', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_website"><?php _e('Website', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="url" id="district_website" name="district_website" class="regular-text"
                               value="<?php echo esc_attr($_POST['district_website'] ?? $district_website); ?>">
                        <p class="description"><?php _e('District website URL (if available)', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="district_office_hours"><?php _e('Office Hours', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="district_office_hours" name="district_office_hours" class="regular-text"
                               value="<?php echo esc_attr($_POST['district_office_hours'] ?? $district_office_hours); ?>"
                               placeholder="e.g., Monday - Friday: 8:00 AM - 4:00 PM">
                        <p class="description"><?php _e('District office operating hours', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <p class="submit">
            <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php _e('Update District', 'esp-admin-manager'); ?>">
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts'); ?>" class="button"><?php _e('Cancel', 'esp-admin-manager'); ?></a>
        </p>
    </form>
</div>

<style>
.esp-form-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 20px;
}

.esp-form-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 14px;
    padding: 8px 12px;
    background: #f1f1f1;
    border-left: 4px solid #0073aa;
}

.required {
    color: #d63638;
}

.form-table th {
    width: 200px;
}
</style>
